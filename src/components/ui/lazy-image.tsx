'use client';

import { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface LazyImageProps {
	src: string;
	alt: string;
	className?: string;
	placeholder?: string;
	onLoad?: () => void;
	onError?: () => void;
}

export function LazyImage({
	src,
	alt,
	className,
	placeholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PC9zdmc+',
	onLoad,
	onError
}: LazyImageProps) {
	const [isLoaded, setIsLoaded] = useState(false);
	const [isInView, setIsInView] = useState(false);
	const [hasError, setHasError] = useState(false);
	const imgRef = useRef<HTMLImageElement>(null);

	useEffect(() => {
		const observer = new IntersectionObserver(
			([entry]) => {
				if (entry && entry.isIntersecting) {
					setIsInView(true);
					observer.disconnect();
				}
			},
			{ threshold: 0.1 }
		);

		if (imgRef.current) {
			observer.observe(imgRef.current);
		}

		return () => observer.disconnect();
	}, []);

	const handleLoad = () => {
		setIsLoaded(true);
		onLoad?.();
	};

	const handleError = () => {
		setHasError(true);
		onError?.();
	};

	return (
		<div className={cn('relative overflow-hidden', className)}>
			<img
				ref={imgRef}
				src={isInView ? src : placeholder}
				alt={alt}
				className={cn(
					'transition-opacity duration-300',
					isLoaded ? 'opacity-100' : 'opacity-0',
					hasError && 'opacity-50'
				)}
				onLoad={handleLoad}
				onError={handleError}
				loading="lazy"
			/>
			{!isLoaded && !hasError && <div className="absolute inset-0 bg-muted animate-pulse" />}
			{hasError && (
				<div className="absolute inset-0 flex items-center justify-center bg-muted text-muted-foreground text-sm">
					Failed to load image
				</div>
			)}
		</div>
	);
}
